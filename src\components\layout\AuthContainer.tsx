import React, { useState, useEffect } from "react";
import AuthScreen from "../../screens/auth/AuthScreen";
import OtpVerificationScreen from "../../screens/auth/OtpVerificationScreen";
import ForgotPasswordScreen from "../../screens/auth/ForgotPasswordScreen";
import SetNewPasswordScreen from "../../screens/auth/SetNewPasswordScreen";
import { authPostMethod } from "@/utils/auth";

interface SetAuthContainerProps {
  onCloseAuth: () => void;
}

const AuthContainer: React.FC<SetAuthContainerProps> = ({ onCloseAuth }) => {
  const [currentScreen, setCurrentScreen] = useState<
    "auth" | "otp" | "forgotPassword" | "resetPassword"
  >("auth");

  const [email, setEmail] = useState("");
  const [inputType, setInputType] = useState("email");

  const handleSendVerificationCode = (userEmail: string, input: string) => {
    setEmail(userEmail);
    setInputType(input);
    setCurrentScreen("otp");
  };

  const handleForgotPassword = () => {
    setCurrentScreen("forgotPassword");
  };

  const handleBackToAuth = () => {
    setCurrentScreen("auth");
  };

  const handleAccountCreated = async (otp: string[]) => {
    const otpCode = otp.join("");
    const isPhone = /^\+?\d{10,15}$/.test(email);

    const payload = isPhone
      ? { phone: email, otp: otpCode }
      : { email, otp: otpCode };

    try {
      const endpoint = inputType === "email" ? "verify-email" : "verify-phone";
      const response = await authPostMethod(endpoint, payload);

      if (!response.success) {
        alert(response.message || "OTP verification failed");
        return;
      }

      console.log("OTP verified");
      setCurrentScreen("auth");
    } catch (error) {
      console.error("OTP verification error:", error);
      alert("Something went wrong while verifying OTP");
    }
  };

  const handleReset = (userEmail: string) => {
    setEmail(userEmail);
    setCurrentScreen("resetPassword");
  };

  const handlePasswordReset = () => {
    setCurrentScreen("auth");
  };

  // Optional: Called by AuthScreen/SocialLogin to close modal after login
  const onLoginSuccess = () => {
    onCloseAuth?.();
  };

  return (
    <div className="flex justify-center items-center  p-4 xs:p-0">
      <div className="relative bg-white rounded-lg  overflow-hidden">
        <button
          className="absolute top-4 right-4 text-neutral-dark text-2xl"
          onClick={onCloseAuth}
        >
          &times;
        </button>

        {currentScreen === "auth" && (
          <AuthScreen
            onSendVerificationCode={handleSendVerificationCode}
            onForgotPassword={handleForgotPassword}
            onLoginSuccess={onLoginSuccess}
          />
        )}

        {currentScreen === "otp" && (
          <OtpVerificationScreen
            email={email}
            onCreateAccount={handleAccountCreated}
            onSignIn={handleBackToAuth}
          />
        )}

        {currentScreen === "forgotPassword" && (
          <ForgotPasswordScreen onEmailEnter={handleReset} />
        )}

        {currentScreen === "resetPassword" && (
          <SetNewPasswordScreen
            onPasswordReset={handlePasswordReset}
            identifier={email}
          />
        )}
      </div>
    </div>
  );
};

export default AuthContainer;
