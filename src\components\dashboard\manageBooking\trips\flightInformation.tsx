import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plane, User, Phone, Mail, ChevronRight, Download } from "lucide-react";

interface FlightSegment {
  id: string;
  departure_time: string;
  arrival_time: string;
  departure_airport: string;
  arrival_airport: string;
  departure_city: string;
  arrival_city: string;
  departure_code: string;
  arrival_code: string;
  airline: string;
  flight_number: string;
  aircraft: string;
  duration: string;
  departure_date: string;
  arrival_date: string;
}

interface Passenger {
  id: string;
  name: string;
  type: "adult" | "child" | "infant";
  seat?: string;
  frequent_flyer?: string;
  special_assistance?: string;
  meal_preference?: string;
}

interface FlightInformationProps {
  // Trip Header Info
  tripTitle?: string;
  tripDate?: string;
  bookingReference?: string;

  // Flight Details
  segments?: FlightSegment[];

  // Booking Info
  bookingDate?: string;
  bookingStatus?: "confirmed" | "pending" | "cancelled";

  // Passenger Info
  passengers?: Passenger[];

  // Contact Info
  contactSupport?: {
    chat: boolean;
    email: boolean;
  };

  // Additional Options
  showDownloadTicket?: boolean;
  showFlightChanges?: boolean;
  showCancelFlight?: boolean;
}

const FlightInformation: React.FC<FlightInformationProps> = ({
  tripTitle = "LONDON TO CHENNAI",
  tripDate = "Mon, 25 Jun 2025 - Thu, 3 Jul 2025",
  bookingReference = "Booking Reference: Nxvoy 20250354",
  segments = [
    {
      id: "1",
      departure_time: "21:45",
      arrival_time: "07:55*",
      departure_airport: "Heathrow Airport",
      arrival_airport: "Chennai Airport",
      departure_city: "London",
      arrival_city: "Chennai",
      departure_code: "LHR",
      arrival_code: "MAA",
      airline: "Gulf Air Company",
      flight_number: "GF 65",
      aircraft: "Airbus A321neo - Jet",
      duration: "5h 58 min",
      departure_date: "Mon, 25 Jun 2025",
      arrival_date: "Tue, 26 Jun 2025",
    },
    {
      id: "2",
      departure_time: "23:00",
      arrival_time: "13:30*",
      departure_airport: "Dubai Airport",
      arrival_airport: "Chennai Airport",
      departure_city: "Dubai",
      arrival_city: "Chennai",
      departure_code: "DXB",
      arrival_code: "MAA",
      airline: "Gulf Air Company",
      flight_number: "GF 65",
      aircraft: "Airbus A321neo - Jet",
      duration: "3h 30 min",
      departure_date: "Tue, 26 Jun 2025",
      arrival_date: "Tue, 26 Jun 2025",
    },
    {
      id: "3",
      departure_time: "7:50*",
      arrival_time: "",
      departure_airport: "Chennai Airport",
      arrival_airport: "London Stansted",
      departure_city: "Chennai",
      arrival_city: "London",
      departure_code: "MAA",
      arrival_code: "STN",
      airline: "Gulf Air Company",
      flight_number: "GF 65",
      aircraft: "Airbus A321neo - Jet",
      duration: "14h 30m",
      departure_date: "Mon, 1st July 2025",
      arrival_date: "Mon, 1st July 2025",
    },
  ],
  bookingDate = "Nxvoy 20250354",
  bookingStatus = "confirmed",
  passengers = [
    {
      id: "1",
      name: "Meentha Ganesh",
      type: "adult",
      seat: "12 A",
      frequent_flyer: "Frequent Flyer Program",
      meal_preference: "Choose Meal",
    },
  ],
  contactSupport = {
    chat: true,
    email: true,
  },
  showDownloadTicket = true,
  showFlightChanges = true,
  showCancelFlight = true,
}) => {
  return (
    <div className="bg-white min-h-screen">
      {/* Header Section - Dark Navy Blue */}
      <div className="bg-[#1E1E76] text-white px-6 py-8 relative rounded-md">
        {/* Background pattern/decoration */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-4 right-8">
            <div className="w-32 h-32 rounded-full border border-white/20"></div>
          </div>
          <div className="absolute top-12 right-16">
            <div className="w-16 h-16 rounded-full border border-white/20"></div>
          </div>
          <div className="absolute bottom-4 right-4">
            <Plane className="w-12 h-12 text-white/30" />
          </div>
        </div>

        <div className="relative z-10 max-w-6xl mx-auto">
          <p className="text-sm text-white/80 mb-1">Your Upcoming Trip</p>
          <h1 className="text-2xl font-bold mb-3">{tripTitle}</h1>
          <p className="text-sm text-white/90 mb-1">{tripDate}</p>
          <p className="text-sm text-white/90">{bookingReference}</p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Back Button */}
        <button className="flex items-center text-[#1E1E76] hover:text-[#1E1E76]/80 transition-colors mb-6">
          <ChevronRight className="w-4 h-4 rotate-180 mr-2" />
          <span className="text-sm">Back to manage Booking</span>
        </button>

        {/* Main Content - Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Flight Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Flight Information Section */}
            <div className="bg-white rounded-lg border border-gray-200">
              {/* Header */}
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  Flight Information
                </h2>
                <div className="flex items-center gap-3">
                  <span className="text-sm text-gray-600">
                    Outward Flight to Chennai (MAA)
                  </span>
                  <Badge className="bg-green-100 text-green-800 text-xs px-2 py-1">
                    {bookingStatus === "confirmed"
                      ? "Confirmed"
                      : bookingStatus}
                  </Badge>
                </div>
              </div>

              {/* Flight Segments */}
              <div className="p-6">
                {segments.map((segment) => (
                  <div key={segment.id} className="mb-8 last:mb-0">
                    {/* Flight Route Header */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-4">
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">
                            {segment.departure_time}
                          </p>
                          <p className="text-sm text-gray-500">
                            {segment.departure_code}
                          </p>
                        </div>

                        {/* Flight Path Visual */}
                        <div className="flex items-center px-4">
                          <div className="w-2 h-2 bg-[#1E1E76] rounded-full"></div>
                          <div className="w-16 h-px bg-gray-300 mx-2 relative">
                            <Plane className="w-4 h-4 text-[#1E1E76] absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                          </div>
                          <div className="w-2 h-2 bg-[#1E1E76] rounded-full"></div>
                        </div>

                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">
                            {segment.arrival_time}
                          </p>
                          <p className="text-sm text-gray-500">
                            {segment.arrival_code}
                          </p>
                        </div>
                      </div>

                      <div className="text-right">
                        <p className="text-sm text-gray-500">
                          {segment.duration}
                        </p>
                      </div>
                    </div>

                    {/* Flight Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <div className="mb-4">
                          <p className="font-semibold text-gray-900 mb-1">
                            {segment.departure_airport} (
                            {segment.departure_code})
                          </p>
                          <p className="text-sm text-gray-600 mb-1">
                            {segment.airline} | Economy |{" "}
                            {segment.flight_number} | {segment.aircraft}
                          </p>
                          <p className="text-sm text-gray-600">
                            Departs {segment.departure_date}
                          </p>
                        </div>
                      </div>

                      <div>
                        <div className="mb-4">
                          <p className="font-semibold text-gray-900 mb-1">
                            {segment.arrival_airport} ({segment.arrival_code})
                          </p>
                          <p className="text-sm text-gray-600">
                            Arrives {segment.arrival_date}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Divider between segments */}
                    {segments.length > 1 &&
                      segment.id !== segments[segments.length - 1].id && (
                        <div className="border-b border-gray-200 mt-6"></div>
                      )}
                  </div>
                ))}
              </div>
            </div>

            {/* Passenger Information */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Passenger Information
              </h3>
              <div className="space-y-6">
                {passengers.map((passenger) => (
                  <div
                    key={passenger.id}
                    className="border-b border-gray-200 pb-6 last:border-b-0 last:pb-0"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-[#1E1E76]/10 rounded-full flex items-center justify-center">
                          <User className="w-5 h-5 text-[#1E1E76]" />
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900 text-base">
                            {passenger.name}
                          </p>
                          <p className="text-sm text-gray-600 capitalize">
                            {passenger.type}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600 mb-1">
                          Seat {passenger.seat}
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-[#1E1E76] p-0 h-auto hover:text-[#1E1E76]/80"
                        >
                          Add Details <ChevronRight className="w-4 h-4 ml-1" />
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-700">
                          Frequent Flyer Program
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-[#1E1E76] p-0 h-auto hover:text-[#1E1E76]/80"
                        >
                          Add Details <ChevronRight className="w-4 h-4 ml-1" />
                        </Button>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-700">
                          Meals
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-[#1E1E76] p-0 h-auto hover:text-[#1E1E76]/80"
                        >
                          Choose Meal <ChevronRight className="w-4 h-4 ml-1" />
                        </Button>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-700">
                          Special Assistance
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-[#1E1E76] p-0 h-auto hover:text-[#1E1E76]/80"
                        >
                          Member Rewards{" "}
                          <ChevronRight className="w-4 h-4 ml-1" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Additional Sections - Full Width Below */}
            <div className="col-span-full space-y-6 mt-6">
              {/* Changes and Cancellations */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Changes and Cancellations
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {showFlightChanges && (
                    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-[#1E1E76]/30 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                          <Plane className="w-4 h-4 text-orange-600" />
                        </div>
                        <span className="font-medium text-gray-900">
                          Flight Changes
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-[#1E1E76] hover:text-[#1E1E76]/80"
                      >
                        Change Flight <ChevronRight className="w-4 h-4 ml-1" />
                      </Button>
                    </div>
                  )}
                  {showCancelFlight && (
                    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-[#1E1E76]/30 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                          <Plane className="w-4 h-4 text-red-600" />
                        </div>
                        <span className="font-medium text-gray-900">
                          Cancel Flight
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-[#1E1E76] hover:text-[#1E1E76]/80"
                      >
                        Cancel Flight <ChevronRight className="w-4 h-4 ml-1" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              {/* Other Information */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Other Information
                </h3>
                <div className="space-y-4 text-gray-600 text-sm leading-relaxed">
                  <p>
                    Once confirmed, airline change penalties and restrictions
                    apply. Meal tickets are not transferable. See your airline's
                    full terms and conditions. Before a flight you are not
                    transferable. Venue tickets are not transferable. See your
                    airline's full terms and conditions. Before a flight you are
                    not transferable.
                  </p>
                  <p>
                    You are not required to confirm flights. All passengers will
                    need a valid passport and you may need a visa depending on
                    your destination and/or connecting airports.
                  </p>
                  <p>
                    The airline will email confirmation directly. All passengers
                    will need a valid passport and you may need a visa depending
                    on your destination and/or connecting airports.
                  </p>
                </div>
                <Button
                  variant="ghost"
                  className="mt-4 text-[#1E1E76] p-0 hover:text-[#1E1E76]/80"
                >
                  Read More <ChevronRight className="w-4 h-4 ml-1" />
                </Button>
              </div>

              {/* Before You Fly */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Before You Fly
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-start gap-3 p-3 border border-gray-200 rounded-lg">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <User className="w-4 h-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900 mb-1">
                          XCover Travel Insurance
                        </p>
                        <p className="text-sm text-gray-600 mb-2">Get quote</p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-[#1E1E76] p-0 h-auto hover:text-[#1E1E76]/80"
                        >
                          Get quote <ChevronRight className="w-4 h-4 ml-1" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-start gap-3 p-3 border border-gray-200 rounded-lg">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <User className="w-4 h-4 text-green-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900 mb-1">
                          Dietary requirements
                        </p>
                        <p className="text-sm text-gray-600 mb-2">
                          Choose Meals
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-[#1E1E76] p-0 h-auto hover:text-[#1E1E76]/80"
                        >
                          Choose Meals <ChevronRight className="w-4 h-4 ml-1" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-start gap-3 p-3 border border-gray-200 rounded-lg">
                      <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <User className="w-4 h-4 text-purple-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900 mb-1">
                          Disability and mobility assistance
                        </p>
                        <p className="text-sm text-gray-600 mb-2">
                          Request assistance
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-[#1E1E76] p-0 h-auto hover:text-[#1E1E76]/80"
                        >
                          Request assistance{" "}
                          <ChevronRight className="w-4 h-4 ml-1" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-start gap-3 p-3 border border-gray-200 rounded-lg">
                      <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <Plane className="w-4 h-4 text-orange-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900 mb-1">
                          Food, drinks, gifts at High Life Shop
                        </p>
                        <p className="text-sm text-gray-600 mb-2">Order</p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-[#1E1E76] p-0 h-auto hover:text-[#1E1E76]/80"
                        >
                          Order <ChevronRight className="w-4 h-4 ml-1" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Booking Details & Contact Support */}
          <div className="lg:col-span-1 space-y-6">
            {/* Booking Details */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Booking Details
              </h3>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600 mb-1">
                    Booking Reference
                  </p>
                  <p className="font-semibold text-gray-900">{bookingDate}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-1">Status</p>
                  <Badge className="bg-green-100 text-green-800 text-xs px-2 py-1">
                    {bookingStatus === "confirmed"
                      ? "Confirmed"
                      : bookingStatus}
                  </Badge>
                </div>
                {showDownloadTicket && (
                  <Button
                    variant="outline"
                    className="w-full text-[#1E1E76] border-[#1E1E76] hover:bg-[#1E1E76] hover:text-white"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download PDF
                  </Button>
                )}
              </div>
            </div>

            {/* Contact Support */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Contact Support
              </h3>
              <div className="space-y-3">
                {contactSupport.chat && (
                  <Button
                    variant="outline"
                    className="w-full text-[#1E1E76] border-[#1E1E76] hover:bg-[#1E1E76] hover:text-white"
                  >
                    <Phone className="w-4 h-4 mr-2" />
                    Chat Support
                  </Button>
                )}
                {contactSupport.email && (
                  <Button
                    variant="outline"
                    className="w-full text-[#1E1E76] border-[#1E1E76] hover:bg-[#1E1E76] hover:text-white"
                  >
                    <Mail className="w-4 h-4 mr-2" />
                    Email Support
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlightInformation;
