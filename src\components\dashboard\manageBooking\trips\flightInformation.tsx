import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Plane,
  Clock,
  MapPin,
  User,
  Phone,
  Mail,
  ChevronRight,
  Download,
  Shield,
  AlertCircle,
  Info,
} from "lucide-react";

interface FlightSegment {
  id: string;
  departure_time: string;
  arrival_time: string;
  departure_airport: string;
  arrival_airport: string;
  departure_city: string;
  arrival_city: string;
  departure_code: string;
  arrival_code: string;
  airline: string;
  flight_number: string;
  aircraft: string;
  duration: string;
  departure_date: string;
  arrival_date: string;
}

interface Passenger {
  id: string;
  name: string;
  type: "adult" | "child" | "infant";
  seat?: string;
  frequent_flyer?: string;
  special_assistance?: string;
  meal_preference?: string;
}

interface FlightInformationProps {
  // Trip Header Info
  tripTitle?: string;
  tripDate?: string;
  bookingReference?: string;

  // Flight Details
  segments?: FlightSegment[];

  // Booking Info
  bookingDate?: string;
  bookingStatus?: "confirmed" | "pending" | "cancelled";

  // Passenger Info
  passengers?: Passenger[];

  // Contact Info
  contactSupport?: {
    chat: boolean;
    email: boolean;
  };

  // Additional Options
  showDownloadTicket?: boolean;
  showFlightChanges?: boolean;
  showCancelFlight?: boolean;
}

const FlightInformation: React.FC<FlightInformationProps> = ({
  tripTitle = "LONDON TO CHENNAI",
  tripDate = "Mon, 25 Jun 2025 - Thu, 3 Jul 2025",
  bookingReference = "Booking Reference: Nxvoy 20250354",
  segments = [
    {
      id: "1",
      departure_time: "21:45",
      arrival_time: "07:55*",
      departure_airport: "Heathrow Airport",
      arrival_airport: "Chennai Airport",
      departure_city: "London",
      arrival_city: "Chennai",
      departure_code: "LHR",
      arrival_code: "MAA",
      airline: "Gulf Air Company",
      flight_number: "GF 65",
      aircraft: "Airbus A321neo - Jet",
      duration: "5h 58 min",
      departure_date: "Mon, 25 Jun 2025",
      arrival_date: "Tue, 26 Jun 2025",
    },
    {
      id: "2",
      departure_time: "23:00",
      arrival_time: "13:30*",
      departure_airport: "Dubai Airport",
      arrival_airport: "Chennai Airport",
      departure_city: "Dubai",
      arrival_city: "Chennai",
      departure_code: "DXB",
      arrival_code: "MAA",
      airline: "Gulf Air Company",
      flight_number: "GF 65",
      aircraft: "Airbus A321neo - Jet",
      duration: "3h 30 min",
      departure_date: "Tue, 26 Jun 2025",
      arrival_date: "Tue, 26 Jun 2025",
    },
    {
      id: "3",
      departure_time: "7:50*",
      arrival_time: "",
      departure_airport: "Chennai Airport",
      arrival_airport: "London Stansted",
      departure_city: "Chennai",
      arrival_city: "London",
      departure_code: "MAA",
      arrival_code: "STN",
      airline: "Gulf Air Company",
      flight_number: "GF 65",
      aircraft: "Airbus A321neo - Jet",
      duration: "14h 30m",
      departure_date: "Mon, 1st July 2025",
      arrival_date: "Mon, 1st July 2025",
    },
  ],
  bookingDate = "Nxvoy 20250354",
  bookingStatus = "confirmed",
  passengers = [
    {
      id: "1",
      name: "Meentha Ganesh",
      type: "adult",
      seat: "12 A",
      frequent_flyer: "Frequent Flyer Program",
      meal_preference: "Choose Meal",
    },
  ],
  contactSupport = {
    chat: true,
    email: true,
  },
  showDownloadTicket = true,
  showFlightChanges = true,
  showCancelFlight = true,
}) => {
  return (
    <div className="max-w-4xl mx-auto bg-gray-50 min-h-screen">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-indigo-900 to-purple-900 text-white p-6 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <p className="text-sm opacity-90 mb-2">Your Upcoming Trip</p>
          <h1 className="text-2xl font-bold mb-2">{tripTitle}</h1>
          <p className="text-sm opacity-90 mb-1">{tripDate}</p>
          <p className="text-sm opacity-90">{bookingReference}</p>
        </div>
        {/* Decorative elements */}
        <div className="absolute top-4 right-4 opacity-20">
          <Plane className="w-16 h-16" />
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Back Button */}
        <button className="flex items-center text-indigo-600 hover:text-indigo-800 transition-colors">
          <ChevronRight className="w-4 h-4 rotate-180 mr-2" />
          Back to manage Booking
        </button>

        {/* Flight Information Card */}
        <Card className="p-6">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Flight Information
              </h2>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <span>Booking Reference</span>
                <Badge
                  variant="secondary"
                  className="bg-green-100 text-green-800"
                >
                  {bookingStatus === "confirmed" ? "Confirmed" : bookingStatus}
                </Badge>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Booking Reference</p>
              <p className="font-semibold">{bookingDate}</p>
              {showDownloadTicket && (
                <Button variant="outline" size="sm" className="mt-2">
                  <Download className="w-4 h-4 mr-2" />
                  Download PDF
                </Button>
              )}
            </div>
          </div>

          {/* Flight Segments */}
          <div className="space-y-6">
            {segments.map((segment, index) => (
              <div
                key={segment.id}
                className="border-l-4 border-indigo-200 pl-6 relative"
              >
                <div className="absolute -left-2 top-0 w-4 h-4 bg-indigo-600 rounded-full"></div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Time and Duration */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-gray-900">
                          {segment.departure_time}
                        </p>
                        <p className="text-sm text-gray-600">
                          {segment.departure_code}
                        </p>
                      </div>
                      <div className="flex-1 text-center">
                        <div className="flex items-center justify-center mb-1">
                          <div className="w-2 h-2 bg-indigo-600 rounded-full"></div>
                          <div className="flex-1 h-px bg-gray-300 mx-2"></div>
                          <Plane className="w-4 h-4 text-indigo-600" />
                          <div className="flex-1 h-px bg-gray-300 mx-2"></div>
                          <div className="w-2 h-2 bg-indigo-600 rounded-full"></div>
                        </div>
                        <p className="text-sm text-gray-600">
                          {segment.duration}
                        </p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-gray-900">
                          {segment.arrival_time}
                        </p>
                        <p className="text-sm text-gray-600">
                          {segment.arrival_code}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Airport Details */}
                  <div className="space-y-4">
                    <div>
                      <p className="font-semibold text-gray-900">
                        {segment.departure_airport} ({segment.departure_code})
                      </p>
                      <p className="text-sm text-gray-600">
                        {segment.airline} | Economy | {segment.flight_number}
                      </p>
                      <p className="text-sm text-gray-600">
                        Departs {segment.departure_date}
                      </p>
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">
                        {segment.arrival_airport} ({segment.arrival_code})
                      </p>
                      <p className="text-sm text-gray-600">
                        Arrives {segment.arrival_date}
                      </p>
                    </div>
                  </div>

                  {/* Additional Info */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Clock className="w-4 h-4" />
                      <span>Duration: {segment.duration}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Plane className="w-4 h-4" />
                      <span>{segment.aircraft}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Contact Support */}
        <div className="flex gap-4">
          {contactSupport.chat && (
            <Button variant="outline" className="flex-1">
              <Phone className="w-4 h-4 mr-2" />
              Chat Support
            </Button>
          )}
          {contactSupport.email && (
            <Button variant="outline" className="flex-1">
              <Mail className="w-4 h-4 mr-2" />
              Email Support
            </Button>
          )}
        </div>

        {/* Passenger Information */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Passenger Information
          </h3>
          <div className="space-y-4">
            {passengers.map((passenger, index) => (
              <div
                key={passenger.id}
                className="border-b border-gray-200 pb-4 last:border-b-0"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-indigo-600" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">
                        {passenger.name}
                      </p>
                      <p className="text-sm text-gray-600 capitalize">
                        {passenger.type}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">
                      Seat {passenger.seat}
                    </p>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-indigo-600 p-0 h-auto"
                    >
                      Add Details <ChevronRight className="w-4 h-4 ml-1" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Other Information */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Other Information
          </h3>
          <div className="prose prose-sm text-gray-600">
            <p>
              Once confirmed, airline change penalties and restrictions apply.
              Meal tickets are not transferable. See your airline's full terms
              and conditions. Before a flight you are not transferable. Venue
              tickets are not transferable. See your airline's full terms and
              conditions. Before a flight you are not transferable.
            </p>
            <p className="mt-3">
              You are not required to confirm flights. All passengers will need
              a valid passport and you may need a visa depending on your
              destination and/or connecting airports.
            </p>
            <p className="mt-3">
              The airline will email confirmation directly. All passengers will
              need a valid passport and you may need a visa depending on your
              destination and/or connecting airports.
            </p>
          </div>
          <Button variant="ghost" className="mt-4 text-indigo-600 p-0">
            Read More <ChevronRight className="w-4 h-4 ml-1" />
          </Button>
        </Card>
      </div>
    </div>
  );
};

export default FlightInformation;
