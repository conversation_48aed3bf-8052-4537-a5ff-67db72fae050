import React from "react";
import TripCard from "./tripCard";

// Example usage of the dynamic TripCard component
const TripCardExample: React.FC = () => {
  const [activeTrip, setActiveTrip] = React.useState<string>("trip-001");

  const handleViewTrip = (tripId: string) => {
    console.log("Viewing trip:", tripId);
    setActiveTrip(tripId);
    // Add your navigation logic here
    // e.g., router.push(`/trips/${tripId}`)
  };

  // Example trip data
  const exampleTrips = [
    {
      id: "trip-001",
      status: "confirmed" as const,
      type: "flight" as const,
      title: "LONDON TO CHENNAI",
      subtitle: undefined,
      date: "2025-05-25",
      duration: "5h 58 min",
      imageUrl:
        "https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=400&h=300&fit=crop",
    },
    {
      id: "trip-002",
      status: "pending" as const,
      type: "hotel" as const,
      title: "PARIS HOTEL BOOKING",
      subtitle: "Luxury Suite",
      date: "2025-06-15",
      duration: "3 nights",
      imageUrl:
        "https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=400&h=300&fit=crop",
    },
    {
      id: "trip-003",
      status: "completed" as const,
      type: "flight" as const,
      title: "NEW YORK TO TOKYO",
      subtitle: "Business Class",
      date: "2025-03-10",
      duration: "14h 20 min",
      imageUrl:
        "https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=400&h=300&fit=crop",
    },
    {
      id: "trip-004",
      status: "cancelled" as const,
      type: "package" as const,
      title: "BALI VACATION PACKAGE",
      subtitle: "All Inclusive",
      date: "2025-07-20",
      duration: "7 days",
      imageUrl: undefined, // This will show the default gradient with icon
    },
  ];

  return (
    <div className="space-y-4 p-6">
      <h2 className="text-2xl font-bold text-brand-black mb-6">My Trips</h2>

      {exampleTrips.map((trip) => (
        <TripCard
          key={trip.id}
          id={trip.id}
          status={trip.status}
          type={trip.type}
          title={trip.title}
          subtitle={trip.subtitle}
          date={trip.date}
          duration={trip.duration}
          imageUrl={trip.imageUrl}
          onViewTrip={handleViewTrip}
          isActive={activeTrip === trip.id}
          className="mb-4"
        />
      ))}
    </div>
  );
};

export default TripCardExample;

// TypeScript interface for trip data (can be moved to a types file)
export interface TripData {
  id: string;
  status: "confirmed" | "pending" | "cancelled" | "completed";
  type: "flight" | "hotel" | "package";
  title: string;
  subtitle?: string;
  date: string;
  duration?: string;
  imageUrl?: string;
}

// Example of how to use with API data
export const TripCardWithApiData: React.FC<{ trips: TripData[] }> = ({
  trips,
}) => {
  const handleViewTrip = (tripId: string) => {
    // Your navigation logic here
    window.location.href = `/trips/${tripId}`;
  };

  return (
    <div className="space-y-4">
      {trips.map((trip) => (
        <TripCard key={trip.id} {...trip} onViewTrip={handleViewTrip} />
      ))}
    </div>
  );
};
