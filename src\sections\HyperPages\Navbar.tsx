"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { signOut } from "next-auth/react";
import axios from "axios";
import Link from "next/link";

import { useCustomSession } from "@/hooks/use-custom-session";
import { clearReduxOnLogout } from "@/store/clearRedux";
import { getInitials } from "@/screens/dashboard/DashboardNavbar";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User as Profile } from "lucide-react";
import AuthContainer from "@/components/layout/AuthContainer";
import { User } from "@/constants/user";

const Navbar: React.FC = () => {
  const router = useRouter();
  const { data: session } = useCustomSession();
  const token = session?.accessToken;

  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSignInClicked, setIsSignInClicked] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (!token) return setLoading(false);
    const fetchUserDetails = async () => {
      try {
        const { data } = await axios.get(
          `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/v1/auth/user/get-details`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              Accept: "application/json",
            },
          }
        );
        setUser(data?.detail?.data);
      } catch (err) {
        console.error("Error fetching user details:", err);
      } finally {
        setLoading(false);
      }
    };
    fetchUserDetails();
  }, [token]);

  const handleLogout = async () => {
    await clearReduxOnLogout();
    signOut({ redirect: false });
  };

  const handleNavigate = (path: string) => {
    router.push(path);
    setIsOpen(false);
  };

  return (
    <>
      <div className="z-50 w-full absolute flex justify-center pt-10 sm:pt-10 md:pt-10 lg:pt-16 xl:pt-16">
        <div className="w-[85%] mx-auto">
          <div className="xs:hidden flex w-full 2xl:h-[58px] xl:h-[58px] lg:h-[48px] md:h-[44px] sm:h-[40px] p-2 justify-around rounded-full bg-[#B4BBE8]">
            <div className="flex 2xl:w-1/5 xl:w-1/5 lg:w-1/5 md:w-1/5 sm:w-1/6 relative left-8 sm:left-2 items-center cursor-pointer">
              <img
                className="xl:w-[110px] lg:w-[100px] 2xl:h-[28px] xl:h-[26px] lg:h-[22px] md:h-[18px] sm:h-[14px]"
                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Logo.png"
                alt="Logo"
              />
            </div>
            <div className="flex font-proxima-nova 2xl:w-5/6 xl:w-5/6 lg:w-5/6 md:w-4/5 sm:w-5/6 2xl:gap-6 xl:gap-6 md:gap-4 sm:gap-2 lg:gap-8 relative right-8 sm:right-2 justify-end items-center">
              <div className="flex gap-4">
                <a
                  className="text-base font-medium px-2 text-black hover:text-gray-700"
                  href="/"
                >
                  Home
                </a>
                <a
                  className="text-base font-medium px-2 text-black hover:text-gray-700"
                  href="/membership"
                >
                  Membership
                </a>
                <a
                  className="text-base font-medium px-2 text-black hover:text-gray-700"
                  href="/about-us"
                >
                  About Us
                </a>
              </div>

              <ChatButton onClick={() => router.push("/chat")} />
              {token ? (
                loading ? (
                  <div className="w-10 h-10 rounded-full bg-gray-300 animate-pulse" />
                ) : user ? (
                  <UserMenu
                    user={user}
                    onLogout={handleLogout}
                    router={router}
                  />
                ) : null
              ) : (
                <SignInButton onClick={() => setIsSignInClicked(true)} />
              )}
            </div>
          </div>

          <div className="md:hidden sm:hidden xs:flex w-full p-2 justify-around items-center rounded-full bg-white shadow-md">
            <div className="relative flex w-1/3 left-2">
              <img
                className="w-20"
                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Logo.png"
                alt="Logo"
              />
            </div>
            <div className="relative flex w-2/3 justify-end right-2">
              <img
                src={
                  isOpen
                    ? "https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Expansion.png"
                    : "https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Vector.png"
                }
                onClick={() => setIsOpen(!isOpen)}
                className="h-6 w-6 cursor-pointer"
                alt="Toggle Menu"
              />
            </div>
          </div>
        </div>
      </div>

      {isOpen && (
        <div className="mt-4 w-full flex justify-center z-50">
          <div className="w-[85%] rounded-[10px] bg-white shadow-md transition-all duration-300">
            <div className="flex justify-end p-2">
              <img
                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Expansion.png"
                alt="Close Menu"
                onClick={() => setIsOpen(false)}
                className="h-6 w-6 cursor-pointer"
              />
            </div>
            <ul className="flex flex-col gap-4 pb-10">
              <div className="w-full divide-y divide-neutral border-t border-neutral">
                <div
                  className={`w-full flex py-4 ${
                    token ? "justify-start pl-6" : "justify-center"
                  }`}
                >
                  {token && user ? (
                    <div
                      onClick={() => {
                        router.push("/userprofile");
                        setIsOpen(false);
                      }}
                      className="flex gap-4 items-center cursor-pointer"
                    >
                      <Avatar>
                        <AvatarImage src={user.profile_picture || ""} />
                        <AvatarFallback className="text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5]">
                          {getInitials(user)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="font-bold ml-2">
                        {user.firstName} {user.lastName ?? ""}
                      </span>
                    </div>
                  ) : (
                    <button
                      onClick={() => {
                        setIsOpen(false);
                        setIsSignInClicked(true);
                      }}
                      className="px-4 py-2 bg-white rounded-[8px]"
                    >
                      Sign In
                    </button>
                  )}
                </div>

                {[
                  { name: "Home", path: "/" },
                  { name: "Membership", path: "/membership" },
                  { name: "About Us", path: "/about-us" },
                ].map((tab) => (
                  <Link key={tab.name} href={tab.path} legacyBehavior>
                    <a
                      onClick={() => setIsOpen(false)}
                      className={`w-full flex justify-center py-4 text-base font-medium ${
                        router.pathname === tab.path
                          ? "bg-[#707FF5] bg-clip-text text-transparent"
                          : "text-black hover:text-gray-700"
                      }`}
                    >
                      {tab.name}
                    </a>
                  </Link>
                ))}
              </div>

              <div className="w-full p-[1px] border-neutral border-t border-b flex justify-center py-2 mt-2 gap-6">
                <div className="flex flex-col font-proxima-nova gap-6 py-4 items-center">
                  <button
                    onClick={() => {
                      router.push("/chat");
                      setIsOpen(false);
                    }}
                    className="flex w-max font-medium px-4 py-1 cursor-pointer bg-brand rounded-[8px] text-white"
                  >
                    <img
                      className="w-5 h-5 mr-[8px]"
                      src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Chat%20White.png"
                      alt="Chat Icon"
                    />
                    Chat with Shasa
                  </button>
                  <button
                    onClick={() => {
                      router.push("/destination");
                      setIsOpen(false);
                    }}
                    className="flex w-max font-medium px-4 py-1 cursor-pointer bg-white text-brand border border-brand rounded-[8px]"
                  >
                    Start Booking
                  </button>
                </div>
              </div>

              {token && user && (
                <div className="flex flex-col justify-start w-full pl-6 gap-4 pt-4">
                  <div
                    onClick={() => {
                      router.push("/userprofile");
                      setIsOpen(false);
                    }}
                    className="flex flex-row items-center cursor-pointer"
                  >
                    <Profile className="fill-black stroke-black w-6 h-6" />
                    <span className="ml-[5px]">My Account</span>
                  </div>
                  <div
                    onClick={async () => {
                      await handleLogout();
                      setIsOpen(false);
                    }}
                    className="flex flex-row items-center cursor-pointer"
                  >
                    <img
                      src="https://storage.googleapis.com/nxvoytrips-img/navbar/sign-out.svg"
                      className="w-4 h-4 mr-2 object-contain"
                      alt="Logout"
                    />
                    <span>Sign out</span>
                  </div>
                </div>
              )}
            </ul>
          </div>
        </div>
      )}

      {isSignInClicked && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="rounded-lg p-4">
            <AuthContainer onCloseAuth={() => setIsSignInClicked(false)} />
          </div>
        </div>
      )}
    </>
  );
};

export default Navbar;

const ChatButton: React.FC<{ onClick: () => void }> = ({ onClick }) => (
  <div className="relative p-0.5 rounded-full before:absolute before:inset-0 before:rounded-full before:bg-[linear-gradient(to_right,rgba(112,127,245,0.9),rgba(161,149,249,0.9),rgba(242,161,242,0.3))] before:-z-10">
    <button
      onClick={onClick}
      className="relative flex items-center gap-2 px-4 py-1 rounded-[8px] bg-brand text-white"
    >
      <img
        className="w-4 h-4"
        src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Chat%20White.png"
        alt="Chat"
      />
      <span>Chat with Shasa</span>
    </button>
  </div>
);

const SignInButton: React.FC<{ onClick: () => void }> = ({ onClick }) => (
  <button
    onClick={onClick}
    className="px-4 py-1 rounded-[8px] bg-white text-brand border border-brand"
  >
    Sign In
  </button>
);

const UserMenu: React.FC<{
  user: User;
  onLogout: () => void;
  router: any;
}> = ({ user, onLogout, router }) => (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Avatar className="cursor-pointer">
        <AvatarImage src={user.profile_picture || ""} />
        <AvatarFallback className="text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5]">
          {getInitials(user)}
        </AvatarFallback>
      </Avatar>
    </DropdownMenuTrigger>
    <DropdownMenuContent
      align="end"
      className="w-[150px] rounded-b-[10px] rounded-t-[0px]"
    >
      <DropdownMenuGroup>
        <DropdownMenuItem onClick={() => router.push("/userprofile")}>
          <Profile className="fill-black stroke-black w-6 h-6" />
          <span>My Account</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onLogout}>
          <img
            src="https://storage.googleapis.com/nxvoytrips-img/navbar/sign-out.svg"
            className="w-4 h-4 mr-2"
            alt="Logout"
          />
          <span>Logout</span>
        </DropdownMenuItem>
      </DropdownMenuGroup>
    </DropdownMenuContent>
  </DropdownMenu>
);
