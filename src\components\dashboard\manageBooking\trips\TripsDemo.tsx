import React from "react";
import Trips from "./index";

// Demo component to showcase the View Trip functionality
const TripsDemo: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Trip Management Demo
          </h1>
          <p className="text-gray-600">
            Click "View Trip" on any trip card to see the detailed flight information.
          </p>
        </div>
        
        {/* Trips Component with View Trip functionality */}
        <Trips />
      </div>
    </div>
  );
};

export default TripsDemo;

// Usage instructions for developers
export const TripsDemoInstructions = () => {
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
      <h3 className="text-lg font-semibold text-blue-900 mb-3">
        How the View Trip Functionality Works:
      </h3>
      <div className="space-y-2 text-blue-800">
        <p>• <strong>Trip Cards:</strong> Display in a tabbed interface (Upcoming/Past trips)</p>
        <p>• <strong>View Trip Button:</strong> Clicking opens the detailed FlightInformation component</p>
        <p>• <strong>Back Navigation:</strong> "Back to Trips" button returns to the trip list</p>
        <p>• <strong>Dynamic Data:</strong> Flight information is populated based on the selected trip</p>
        <p>• <strong>State Management:</strong> Uses React state to toggle between views</p>
      </div>
      
      <div className="mt-4 p-4 bg-white rounded border border-blue-200">
        <h4 className="font-semibold text-blue-900 mb-2">Key Features:</h4>
        <ul className="list-disc list-inside text-blue-800 space-y-1">
          <li>Seamless transition between trip list and detailed view</li>
          <li>Maintains trip context when viewing details</li>
          <li>Responsive design works on all screen sizes</li>
          <li>Easy to extend with real API data</li>
          <li>Consistent UI/UX with the rest of the application</li>
        </ul>
      </div>
    </div>
  );
};
