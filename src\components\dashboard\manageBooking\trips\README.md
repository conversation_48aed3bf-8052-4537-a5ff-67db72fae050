# TripCard Component

A dynamic, reusable trip card component that displays travel booking information with a consistent UI design.

## Features

- ✅ Fully dynamic with props-based configuration
- ✅ Multiple trip types (flight, hotel, package)
- ✅ Status indicators with color coding
- ✅ Responsive design
- ✅ Customizable images with fallback
- ✅ TypeScript support
- ✅ Accessibility compliant

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `id` | `string` | ✅ | Unique identifier for the trip |
| `status` | `'confirmed' \| 'pending' \| 'cancelled' \| 'completed'` | ✅ | Trip status |
| `type` | `'flight' \| 'hotel' \| 'package'` | ✅ | Type of trip |
| `title` | `string` | ✅ | Main title (e.g., "LONDON TO CHENNAI") |
| `subtitle` | `string` | ❌ | Optional subtitle (e.g., "Business Class") |
| `date` | `string` | ✅ | Trip date (ISO format or readable string) |
| `duration` | `string` | ❌ | Trip duration (e.g., "5h 58 min") |
| `imageUrl` | `string` | ❌ | Custom image URL (falls back to gradient with icon) |
| `onViewTrip` | `(tripId: string) => void` | ✅ | Callback when "View Trip" is clicked |
| `className` | `string` | ❌ | Additional CSS classes |

## Usage Examples

### Basic Usage

```tsx
import TripCard from './tripCard';

const MyTrips = () => {
  const handleViewTrip = (tripId: string) => {
    router.push(`/trips/${tripId}`);
  };

  return (
    <TripCard
      id="trip-001"
      status="confirmed"
      type="flight"
      title="LONDON TO CHENNAI"
      date="2025-05-25"
      duration="5h 58 min"
      onViewTrip={handleViewTrip}
    />
  );
};
```

### With Custom Image

```tsx
<TripCard
  id="trip-002"
  status="confirmed"
  type="flight"
  title="LONDON TO CHENNAI"
  subtitle="Economy Class"
  date="2025-05-25"
  duration="5h 58 min"
  imageUrl="https://example.com/flight-image.jpg"
  onViewTrip={handleViewTrip}
/>
```

### Hotel Booking

```tsx
<TripCard
  id="hotel-001"
  status="pending"
  type="hotel"
  title="PARIS LUXURY HOTEL"
  subtitle="Deluxe Suite"
  date="2025-06-15"
  duration="3 nights"
  onViewTrip={handleViewTrip}
/>
```

### With API Data

```tsx
const TripsList = ({ trips }: { trips: TripData[] }) => {
  return (
    <div className="space-y-4">
      {trips.map((trip) => (
        <TripCard
          key={trip.id}
          {...trip}
          onViewTrip={handleViewTrip}
        />
      ))}
    </div>
  );
};
```

## Status Colors

- **Confirmed**: Green (`bg-success`)
- **Pending**: Yellow (`bg-warning`)
- **Cancelled**: Red (`bg-error`)
- **Completed**: Gray (`bg-neutral`)

## Type Icons

- **Flight**: Plane icon
- **Hotel**: Map pin icon
- **Package**: Plane icon (default)

## Styling

The component uses Tailwind CSS classes and follows the project's design system:

- Brand colors from `tailwind.config.ts`
- Consistent spacing and typography
- Hover effects and transitions
- Responsive design

## Dependencies

- `@/components/ui/button`
- `@/components/ui/card`
- `@/components/ui/badge`
- `lucide-react` icons

## Accessibility

- Semantic HTML structure
- Proper ARIA labels
- Keyboard navigation support
- Screen reader friendly
