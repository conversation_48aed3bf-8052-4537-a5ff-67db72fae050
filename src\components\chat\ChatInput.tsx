import { Send, Plus, Zap, Search, Mic } from "lucide-react";
import React, { KeyboardEvent, useRef, useEffect, useState } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { useIsCollapsed } from "@/hooks/sidebar/useIsCollapsed";
import axios from "axios";
import { useCustomSession } from "@/hooks/use-custom-session";
import { Textarea } from "../ui/textarea";

interface ChatInputProps {
  message: string;
  chatMessages: any[];
  setMessage: (msg: string) => void;
  sendMessage: (customText?: string) => void;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  message,
  chatMessages,
  setMessage,
  sendMessage,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const isMobile = useIsMobile();
  const isCollapsed = useIsCollapsed();

  const [suggestions, setSuggestions] = useState<string[]>([]);

  const { data: session, status } = useCustomSession();
  const token = session?.accessToken;

  const [placeholderText, setPlaceholderText] = useState("");
  const placeholderMessages = [
    "Direct flight from Mumbai to Singapore next weekend—2 adults, 1 child. Show all options",
    "Plan a 5-day family trip to Thailand from Delhi, Oct 2. Budget is ₹2,00,000. Indian citizen",
    "Cheapest round-trip Bangalore to Delhi, Sep 10–20. Solo trip",
  ];

  useEffect(() => {
    if (!isFocused && message === "") {
      let messageIndex = 0;
      let charIndex = 0;
      let isDeleting = false;
      let timeoutId: any;

      const typeWriter = () => {
        const currentMessage = placeholderMessages[messageIndex];

        if (!isDeleting) {
          // Typing forward
          setPlaceholderText(currentMessage.slice(0, charIndex + 1));
          charIndex++;

          if (charIndex === currentMessage.length) {
            // Finished typing, wait then start deleting
            timeoutId = setTimeout(() => {
              isDeleting = true;
              typeWriter(); // Continue the loop
            }, 2000);
            return;
          }
        } else {
          // Deleting backward
          setPlaceholderText(currentMessage.slice(0, charIndex - 1));
          charIndex--;

          if (charIndex === 0) {
            // Finished deleting, move to next message
            isDeleting = false;
            messageIndex = (messageIndex + 1) % placeholderMessages.length;
            timeoutId = setTimeout(() => {
              typeWriter(); // Continue the loop
            }, 500);
            return;
          }
        }

        // Continue typing/deleting
        timeoutId = setTimeout(typeWriter, isDeleting ? 75 : 150);
      };

      // Start the typing effect
      typeWriter();

      return () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      };
    } else {
      setPlaceholderText("Type a message");
    }
  }, [isFocused, message]);

  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!message || message.trim().length <= 2) {
      setSuggestions([]);
      return;
    }

    // Clear existing debounce
    if (debounceRef.current) clearTimeout(debounceRef.current);

    // Set debounce delay
    debounceRef.current = setTimeout(() => {
      // console.log("chatMessages======", chatMessages);
      const fetchSuggestions = async () => {
        try {
          const response = await axios.post(
            `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/suggestions`,
            {
              messages: chatMessages
                .filter((m: any) => m.text?.trim() !== "")
                .map((m: any) => ({
                  role: m.sender,
                  content: m.text,
                })),
              current_input: message,
            },
            {
              headers: {
                Authorization: `Bearer ${token}`,
                Accept: "application/json",
              },
            }
          );
          console.log("suggestion response=======", response);
          setSuggestions(response.data?.detail?.data?.suggestions || []);
        } catch (error) {
          console.error("Suggestion fetch failed:", error);
          setSuggestions([]); // Clear on error
        }
      };

      fetchSuggestions();
    }, 300); // debounce delay

    // Clear timeout on cleanup
    return () => {
      if (debounceRef.current) clearTimeout(debounceRef.current);
    };
  }, [message, chatMessages, token]);

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const text = message.trim();
    if (!text) return;
    sendMessage(text);
    setMessage("");
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      const text = message.trim();
      if (!text) return;
      sendMessage(text);
      setMessage("");
    }
  };

  const handleSuggestionClick = (text: string) => {
    setMessage((prev) => {
      const trimmed = prev.trimEnd();
      return trimmed ? `${trimmed} ${text}` : text;
    });
    textareaRef.current?.focus();
  };

  // Auto-resize textarea height
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto";
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, [message]);

  // Calculate positioning based on screen size and sidebar state
  const getPositioning = () => {
    if (isMobile) {
      return {
        className: "fixed bottom-0 left-0 right-0 z-50 w-full",
        style: { width: "100%" },
      };
    } else {
      const leftPosition = isCollapsed ? "3rem" : "16rem";
      return {
        className:
          "fixed bottom-0 right-0 z-50 transition-[left] ease-linear duration-200",
        style: { left: leftPosition },
      };
    }
  };

  const positioning = getPositioning();

  return (
    <div className={positioning.className} style={positioning.style}>
      {/* Content Container with responsive padding */}
      <div className={`flex justify-center mb-6`}>
        <div className="w-full max-w-3xl mx-6">
          {/* Main Input Container */}
          <div className="relative ">
            <form onSubmit={onSubmit} className="relative ">
              <div
                className={`
                   flex flex-col bg-white rounded-2xl border-2 transition-all duration-200
                  ${
                    isFocused
                      ? "border-[#1E1E76] shadow-lg shadow-blue-500/10"
                      : "border-gray-200 hover:border-gray-300"
                  }
                `}
              >
                <div className="flex items-end pr-2 pb-2">
                  <div className="w-full flex">
                    {/* <button
                    type="button"
                    className={`${
                      isMobile ? 'p-1.5' : 'p-2'
                    } text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200`}
                    title="Add attachment"
                  >
                    <Plus className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
                  </button> */}
                    {/* {!isMobile && (
                    <button
                      type="button"
                      className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200 ml-1"
                      title="AI assistance"
                    >
                      <Zap className="h-5 w-5" />
                    </button>
                  )} */}
                    <div className={`w-[90%] mt-2`}>
                      <textarea
                        ref={textareaRef}
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        onKeyDown={handleKeyDown}
                        onFocus={() => setIsFocused(true)}
                        onBlur={() => setIsFocused(false)}
                        placeholder={placeholderText}
                        rows={2}
                        className={`w-full border-none ml-2 outline-none bg-transparent resize-none`}
                      />
                    </div>
                  </div>

                  <div className={`flex items-center `}>
                    <button
                      type="submit"
                      disabled={!message.trim()}
                      className={`
                      ${isMobile ? "ml-1 p-1.5" : "ml-2 p-2"} rounded-lg transition-all duration-200
                      ${
                        message.trim()
                          ? "bg-[#1E1E76] hover:bg-blue-700 text-white shadow-md hover:shadow-lg"
                          : "bg-gray-100 text-gray-400 cursor-not-allowed"
                      }
                    `}
                      title="Send message"
                    >
                      <Send className={`${isMobile ? "h-4 w-4" : "h-5 w-5"}`} />
                    </button>
                  </div>
                </div>
                <div className={`flex flex-wrap gap-2 mb-2 px-2`}>
                  {suggestions.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {suggestions.map((text, index) => (
                        <div
                          key={index}
                          onClick={() => handleSuggestionClick(text)}
                          className="md:text-sm text-xs bg-gray-50 border hover:bg-gray-200 text-black md:px-4 px-2 py-1 mt-1 rounded-full transition"
                        >
                          {text}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};
