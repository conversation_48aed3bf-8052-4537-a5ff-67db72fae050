import React from "react";
import FlightInformation from "./flightInformation";

// Example usage of the dynamic FlightInformation component
const FlightInformationExample: React.FC = () => {
  // Example flight data - this would typically come from an API or props
  const exampleFlightData = {
    tripTitle: "LONDON TO CHENNAI",
    tripDate: "Mon, 25 Jun 2025 - Thu, 3 Jul 2025",
    bookingReference: "Booking Reference: Nxvoy 20250354",
    segments: [
      {
        id: "1",
        departure_time: "21:45",
        arrival_time: "07:55*",
        departure_airport: "Heathrow Airport",
        arrival_airport: "Dubai Airport",
        departure_city: "London",
        arrival_city: "Dubai",
        departure_code: "LHR",
        arrival_code: "DXB",
        airline: "Gulf Air Company",
        flight_number: "GF 65",
        aircraft: "Airbus A321neo - Jet",
        duration: "5h 58 min",
        departure_date: "Mon, 25 Jun 2025",
        arrival_date: "Tue, 26 Jun 2025"
      },
      {
        id: "2",
        departure_time: "23:00",
        arrival_time: "13:30*",
        departure_airport: "Dubai Airport",
        arrival_airport: "Chennai Airport",
        departure_city: "Dubai",
        arrival_city: "Chennai",
        departure_code: "DXB",
        arrival_code: "MAA",
        airline: "Gulf Air Company",
        flight_number: "GF 142",
        aircraft: "Boeing 787-9",
        duration: "3h 30 min",
        departure_date: "Tue, 26 Jun 2025",
        arrival_date: "Tue, 26 Jun 2025"
      },
      {
        id: "3",
        departure_time: "7:50*",
        arrival_time: "22:20",
        departure_airport: "Chennai Airport",
        arrival_airport: "London Stansted",
        departure_city: "Chennai",
        arrival_city: "London",
        departure_code: "MAA",
        arrival_code: "STN",
        airline: "Gulf Air Company",
        flight_number: "GF 143",
        aircraft: "Boeing 787-9",
        duration: "14h 30m",
        departure_date: "Mon, 1st July 2025",
        arrival_date: "Mon, 1st July 2025"
      }
    ],
    bookingDate: "Nxvoy 20250354",
    bookingStatus: "confirmed" as const,
    passengers: [
      {
        id: "1",
        name: "Meentha Ganesh",
        type: "adult" as const,
        seat: "12A",
        frequent_flyer: "Frequent Flyer Program",
        meal_preference: "Vegetarian"
      },
      {
        id: "2",
        name: "Rajesh Kumar",
        type: "adult" as const,
        seat: "12B",
        frequent_flyer: "Gold Member",
        meal_preference: "Non-Vegetarian"
      }
    ],
    contactSupport: {
      chat: true,
      email: true
    },
    showDownloadTicket: true,
    showFlightChanges: true,
    showCancelFlight: true
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <FlightInformation {...exampleFlightData} />
    </div>
  );
};

export default FlightInformationExample;

// Example of how to use with different trip types
export const ShortFlightExample: React.FC = () => {
  const shortFlightData = {
    tripTitle: "NEW YORK TO BOSTON",
    tripDate: "Fri, 15 Dec 2025",
    bookingReference: "Booking Reference: Nxvoy 20251215",
    segments: [
      {
        id: "1",
        departure_time: "08:30",
        arrival_time: "10:15",
        departure_airport: "John F. Kennedy International Airport",
        arrival_airport: "Logan International Airport",
        departure_city: "New York",
        arrival_city: "Boston",
        departure_code: "JFK",
        arrival_code: "BOS",
        airline: "American Airlines",
        flight_number: "AA 1234",
        aircraft: "Boeing 737-800",
        duration: "1h 45 min",
        departure_date: "Fri, 15 Dec 2025",
        arrival_date: "Fri, 15 Dec 2025"
      }
    ],
    passengers: [
      {
        id: "1",
        name: "John Smith",
        type: "adult" as const,
        seat: "14C"
      }
    ],
    contactSupport: {
      chat: true,
      email: true
    }
  };

  return <FlightInformation {...shortFlightData} />;
};

// Example with multiple passengers including children
export const FamilyFlightExample: React.FC = () => {
  const familyFlightData = {
    tripTitle: "PARIS TO TOKYO",
    tripDate: "Wed, 20 Aug 2025 - Sun, 30 Aug 2025",
    bookingReference: "Booking Reference: Nxvoy 20250820",
    passengers: [
      {
        id: "1",
        name: "Sarah Johnson",
        type: "adult" as const,
        seat: "15A"
      },
      {
        id: "2",
        name: "Michael Johnson",
        type: "adult" as const,
        seat: "15B"
      },
      {
        id: "3",
        name: "Emma Johnson",
        type: "child" as const,
        seat: "15C"
      },
      {
        id: "4",
        name: "Baby Johnson",
        type: "infant" as const,
        seat: "15B" // Lap infant
      }
    ],
    contactSupport: {
      chat: true,
      email: true
    }
  };

  return <FlightInformation {...familyFlightData} />;
};
