import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plane, MapPin } from "lucide-react";

interface TripCardProps {
  id: string;
  status?: "confirmed" | "pending" | "cancelled" | "completed";
  type: "flight" | "hotel" | "package";
  title: string;
  subtitle?: string;
  date: string;
  duration?: string;
  imageUrl?: string;
  onViewTrip: (tripId: string) => void;
  className?: string;
  isActive?: boolean;
}

const TripCard: React.FC<TripCardProps> = ({
  id,
  status,
  type,
  title,
  subtitle,
  date,
  duration,
  imageUrl,
  onViewTrip,
  className = "",
  isActive = false,
}) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "confirmed":
        return "bg-success text-success-foreground";
      case "pending":
        return "bg-warning text-warning-foreground";
      case "cancelled":
        return "bg-error text-error-foreground";
      case "completed":
        return "bg-neutral text-neutral-foreground";
      default:
        return "bg-neutral text-neutral-foreground";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type?.toLowerCase()) {
      case "flight":
        return <Plane className="w-4 h-4" />;
      case "hotel":
        return <MapPin className="w-4 h-4" />;
      default:
        return <Plane className="w-4 h-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      });
    } catch {
      return dateString;
    }
  };

  return (
    <div className={`relative ${className} mt-4`}>
      {/* Status Badge - Only show if status exists */}
      {status && (
        <Badge
          className={`absolute -top-3 left-2 z-10 px-3 py-[2px] text-xs font-medium rounded-full ${getStatusColor(status)}`}
          // variant="destructive"
        >
          {status?.charAt(0).toUpperCase() + status?.slice(1)}
        </Badge>
      )}

      {/* Main Card */}
      <Card
        className={`flex justify-between p-4 bg-white border border-neutral rounded-lg shadow-sm  ${status ? "mt-3" : ""}`}
      >
        {/* Left Section */}
        <div className="flex  space-x-4 w-full">
          {/* Trip Image */}
          <div className="w-[88px] aspect-square h-[88px] rounded-lg overflow-hidden bg-gradient-to-br from-orange-400 to-pink-500 flex-shrink-0">
            {imageUrl ? (
              <img
                src={imageUrl}
                alt={title}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-white">
                {getTypeIcon(type)}
              </div>
            )}
          </div>

          {/* Trip Details */}
          <div className="flex flex-col justify-between align-content-between w-full">
            {/* Type with Icon */}

            <Badge
              className="flex flex-row w-content items-center mb-1"
              variant="outline"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="15"
                viewBox="0 0 16 15"
                fill="none"
              >
                <path
                  d="M10.2898 8.5L6.5 14.5L5 14.5L6.8945 8.5L2.8745 8.5L1.625 10.75L0.500001 10.75L1.25 7.375L0.500002 4L1.625 4L2.87525 6.25L6.89525 6.25L5 0.25L6.5 0.25L10.2898 6.25L14.375 6.25C14.6734 6.25 14.9595 6.36853 15.1705 6.57951C15.3815 6.79048 15.5 7.07663 15.5 7.375C15.5 7.67337 15.3815 7.95952 15.1705 8.1705C14.9595 8.38148 14.6734 8.5 14.375 8.5L10.2898 8.5Z"
                  fill="#8C8C8C"
                />
              </svg>
              <span className="ml-2 text-sm font-medium capitalize text-brand-grey">
                {type}
              </span>
            </Badge>

            <div className="flex flex-row items-center justify-between w-full">
              <div>
                {/* Title */}
                <h3 className="text-lg font-bold text-gray-900 mb-[2px]">
                  {title}
                </h3>

                {/* Date and Duration */}
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <span>{formatDate(date)}</span>
                  {duration && (
                    <>
                      <span>-</span>
                      <span>{duration}</span>
                    </>
                  )}
                  {subtitle && (
                    <>
                      <span>•</span>
                      <span>{subtitle}</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Right Section - Action Button */}
          <div className="flex flex-col justify-end h-full">
            <Button
              onClick={() => onViewTrip(id)}
              className="bg-brand text-white hover:bg-brand/200 hover:text-white px-6 py-2 rounded-lg font-medium transition-colors"
            >
              View Trip
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default TripCard;
