import React, { useState } from "react";
import LoginForm from "../../components/auth/LoginForm";
import SignupForm from "../../components/auth/SignupForm";
import SocialLogin from "../../components/auth/SocialLogin";
import { signIn } from "next-auth/react";

interface AuthScreenProps {
  onSendVerificationCode: (email: string, inputType: string) => void;
  onForgotPassword: (email: string) => void;
  onLoginSuccess: (loggedInData: any) => void;
}

const AuthScreen: React.FC<AuthScreenProps> = ({
  onSendVerificationCode,
  onForgotPassword,
  onLoginSuccess,
}) => {
  const [isSignIn, setIsSignIn] = useState(true);

  const toggleSignUp = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSignIn(!isSignIn);
  };

  // State to track the email input
  const [email, setEmail] = useState("");

  // Update email state from both forms
  const handleEmailChange = (newEmail: string) => {
    setEmail(newEmail);
  };

  // Handler to collect email and trigger verification
  const handleVerificationRequest = (userEmail: string, inputType: string) => {
    onSendVerificationCode(userEmail, inputType);
  };

  return (
    <div className="md:flex lg:flex-row sm:flex-col font-proxima-nova bg-brand-white">
      {/* Form section (left side on desktop) */}
      <div className="lg:p-6 sm:p-6 xs:p-6 lg:w-7/12 font-proxima-nova sm:w-full md:relative">
        <div>
          <h1
            className="text-xl sm:text-2xl md:text-2xl lg:text-4xl lg:mb-2 sm:mb-2 xs:mb-1 text-center md:text-left"
            style={styles.welcome}
          >
            {isSignIn
              ? `Welcome Back, Traveller!`
              : `Your Journey Begins Here!`}
          </h1>

          <p className="text-brand-black 2xl:mb-6 xl:mb-2 lg:mb-2 sm:mb-3 xs:mb-2 text-sm sm:text-base xs:text-sm text-center md:text-left">
            {isSignIn
              ? `Sign in to pick up right where you left off. We hope you're enjoying planning your trips with Shasa!`
              : `Sign up to explore, plan, and create the perfect itinerary for your trips. Your next voyage starts now!`}
          </p>

          {isSignIn && (
            <p className="lg:mb-6 sm:mb-3 xs:mb-2 font-proxima-nova text-sm sm:text-base text-center md:text-left text-neutral-dark">
              Still travelling without a NxVoy account?{" "}
              <a
                href="#"
                className="text-brand-black italic"
                onClick={toggleSignUp}
              >
                Sign Up Here!
              </a>
            </p>
          )}

          {isSignIn ? (
            <LoginForm
              onForgotPassword={() => onForgotPassword(email)}
              onLoginSuccess={onLoginSuccess}
            />
          ) : (
            <SignupForm onVerificationRequest={handleVerificationRequest} />
          )}

          {!isSignIn && (
            <p className="2xl:my-4 xl:my-3 lg:my-3 md:my-2 xs:my-2 text-sm md:text-base md:text-left font-proxima-nova text-neutral-dark">
              Already have an account?{" "}
              <a
                href="#"
                className="text-brand-black italic"
                onClick={toggleSignUp}
              >
                Sign In!
              </a>
            </p>
          )}

          {!isSignIn && (
            <p className="2xl:my-3 xl:my-3 lg:my-3 text-neutral-dark md:my-2 xs:my-2 md:text-sm font-proxima-nova md:text-left text-xs">
              By continuing you agree to NxVoy's{" "}
              <a href="/terms" className="underline ">
                Terms of Service
              </a>{" "}
              and acknowledge you've read our{" "}
              <a href="/privacy" className="underline">
                Privacy Policy
              </a>
            </p>
          )}
        </div>

        {/* Vertical divider for desktop only */}
        <div className="hidden lg:flex md:flex-col md:justify-center md:items-center md:h-full md:absolute md:inset-y-0 md:right-0 md:transform md:translate-x-1/2">
          <div className="w-px h-24 bg-neutral"></div>
          <div className="w-px h-24 bg-neutral"></div>
          <div className="w-px h-24 bg-neutral"></div>
          <span className="py-4 text-sm text-neutral-dark">or</span>
          <div className="w-px h-24 bg-neutral"></div>
          <div className="w-px h-24 bg-neutral"></div>
          <div className="w-px h-24 bg-neutral"></div>
        </div>
        <div className="lg:hidden xs:flex sm:flex sm:flex-row sm:justify-center sm:items-center sm:absolute sm:inset-x-0 sm:right-0 sm:transform sm:translate-y-1/2 xs:flex-row xs:justify-center xs:items-center xs:absolute xs:inset-x-0 xs:right-0 xs:transform xs:translate-y-1/2">
          <div className="w-24 h-px bg-neutral"></div>
          <div className="w-24 h-px bg-neutral"></div>
          <div className="w-24 h-px bg-neutral"></div>
          <span className="px-4 text-sm text-neutral-dark">or</span>
          <div className="w-24 h-px bg-neutral"></div>
          <div className="w-24 h-px bg-neutral"></div>
          <div className="w-24 h-px bg-neutral"></div>
        </div>
      </div>

      <div className="lg:p-8 sm:p-6 xs:p-4 lg:w-5/12 sm:w-full sm:flex sm:flex-col sm:justify-center xs:justify-center sm:items-center xs:items-center sm:border-gray-200">
        {/* Only show this divider on mobile */}
        <div className="flex items-center my-6 xs:hidden sm:hidden">
          <div className="flex-1 h-px bg-neutral"></div>
          <span className="px-4 text-sm text-neutral-dark">or</span>
          <div className="flex-1 h-px bg-neutral"></div>
        </div>

        <SocialLogin
          authType={isSignIn ? "signIn" : "signup"}
          onLoginSuccess={onLoginSuccess}
        />
      </div>
    </div>
  );
};

const styles = {
  welcome: {
    fontFamily: "Proxima Nova, sans-serif",
    fontWeight: 700,
    letterSpacing: "0%",
    WebkitBackgroundClip: "text",
    color: "#080236",
  },
};

export default AuthScreen;
