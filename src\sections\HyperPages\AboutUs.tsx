"use client";

import React from "react";
import Footer from "./Footer";
import { AboutUSBelives } from "@/constants/aboutUs";
import { AboutTeam } from "@/constants/aboutUs";
import { HowShasaSteps } from "@/constants/aboutUs";
import Navbar from "./Navbar";
import { useRouter } from "next/router";

const AboutUs = () => {
  const router = useRouter();

  return (
    <div className="flex flex-col min-h-screen bg-white text-[#0e0b2b] font-proxima-nova">
      <div
        className="w-full min-h-screen bg-cover bg-center relative flex flex-col"
        style={{
          backgroundImage:
            "url('https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/HomePage%20Banner.png')",
        }}
      >
        <Navbar />
        <div className="flex-grow flex flex-col md:flex-row w-[90%] md:w-[85%] mx-auto justify-between items-center mt-12 md:mt-24">
          <h1 className="text-3xl md:text-5xl font-bold leading-snug bg-gradient-to-r from-[#707FF5] via-[#A195F9] to-[#F2A1F2] text-transparent bg-clip-text text-center md:text-left max-w-full md:max-w-[60%]">
            Know More About
            <br />
            Your AI Trip Planner
          </h1>
          <img
            src="https://storage.googleapis.com/nxvoytrips-img/HyperPages/ShasaImg.png"
            alt="ShasaImg Letters"
            className="w-full max-w-[320px] md:max-w-[41%] mt-12 md:mt-0"
          />
        </div>
      </div>
      <section className="w-full px-4 md:px-6 py-12 flex flex-col items-center gap-12 text-[#0e0b2b] font-proxima-nova">
        <div className="max-w-6xl flex flex-col gap-4 text-center md:text-left">
          <h1 className="text-[#4B4BC3] text-3xl md:text-4xl font-bold">
            Hey there, traveller!
          </h1>
          <p className="text-sm md:text-base leading-relaxed">
            I’m Shasa, the brain behind <strong>NxVoy</strong>, which stands for{" "}
            <em>'Next Voyage'</em>. I'm your smart, intuitive, and effortless
            travel companion. And I call myself your companion because I don’t
            just plan your trips but personally travel with you, just like your
            best companion does. The only difference is that I travel with you
            virtually! But before becoming your go-to AI companion, let’s rewind
            to where it all started.
          </p>
        </div>

        <div className="w-full flex flex-col md:flex-row items-center justify-center gap-8 mt-8 md:mt-12 md:px-0">
          <div className="relative w-full max-w-[400px] h-[480px] rounded-[16px] overflow-hidden">
            <div className="absolute inset-0 flex justify-center items-center z-0">
              <div className="w-[250px] h-[250px] bg-white blur-[80px] rounded-full"></div>
            </div>
            <img
              src="https://storage.googleapis.com/nxvoytrips-img/HyperPages/AboutUs-Img1.png"
              alt="About Us"
              className="relative z-10 w-full h-full object-cover rounded-[16px]"
            />
          </div>
          <div className="w-full md:w-[50%] flex flex-col justify-center gap-4 text-center md:text-left mt-6 md:mt-0 px-4">
            <h2 className="text-2xl md:text-4xl font-bold">
              A Problem Begging for a Solution
            </h2>
            <p className="text-sm md:text-base leading-relaxed">
              Meet my creators – a tech-savvy husband and wife duo, both
              seasoned software engineers, juggling demanding jobs and raising
              two little adventurers. Despite their love for travel, planning a
              perfect trip was a nightmare.
            </p>
            <p className="text-sm md:text-base leading-relaxed">
              They spent hours, sometimes even days, researching destinations,
              comparing flights, sorting accommodations, and managing countless
              tabs. And after three ambitious attempts, guess how many vacations
              actually happened? <strong>ZERO</strong>.
            </p>
            <p className="text-sm md:text-base leading-relaxed">
              They weren’t alone. Millions of travellers faced the same problem
              – overwhelming planning, scattered information, and last-minute
              surprises. They knew something had to change.
            </p>
            <p className="text-sm md:text-base leading-relaxed">
              And just like that, in 2025,{" "}
              <strong>NxVoy was born, and so was I!</strong>
            </p>
          </div>
        </div>

        <div className="w-full md:max-w-[1240px] text-center mt-12 px-4">
          <h2 className="text-2xl md:text-5xl font-bold text-[#4B4BC3] leading-snug">
            Our mission is to make travel effortless,
            <br className="hidden md:block" /> intelligent, and deeply personal.
          </h2>
        </div>

        <div className="max-w-6xl mx-auto flex flex-col items-center gap-12">
          <h2 className="text-2xl md:text-[40px] font-bold text-center leading-tight">
            NxVoy believes in
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 w-full">
            {AboutUSBelives.map(({ title, description, icon }) => (
              <div
                key={title}
                className="flex flex-col items-center bg-white border border-[rgba(30,30,118,1)] rounded-lg p-6 gap-4 text-center shadow-sm hover:shadow-md transition"
              >
                <div className="w-16 h-16 relative">
                  <img
                    src={icon}
                    alt={title}
                    className="w-full h-full object-contain"
                  />
                </div>
                <h3 className="text-xl font-bold">{title}</h3>
                <p className="text-sm text-[#080236]">{description}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="max-w-6xl mx-auto px-4 py-8">
          <h2 className="text-3xl md:text-5xl font-bold text-center mb-12">
            Meet The Team
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {AboutTeam.map((member, index) => (
              <div key={index} className="flex flex-col items-center">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-full h-[300px] object-cover rounded-md"
                />
                <div className="bg-[#EBEAFE] px-4 py-3 w-full min-h-[130px] flex flex-col justify-start">
                  <h3 className="font-bold text-xs">{member.name}</h3>
                  <p className="text-[11px] font-medium">
                    {member.designation}
                  </p>
                  <p className="text-[11px] mt-1">{member.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="max-w-6xl mx-auto px-4 py-16 text-[#0e0b2b] font-proxima-nova">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div className="flex flex-col justify-center">
              <h2 className="text-3xl md:text-5xl font-bold leading-tight mb-4">
                Why NxVoy? <br /> Because Travel Should Feel Like Magic!
              </h2>
              <p className="text-sm md:text-base leading-relaxed text-[#0e0b2b]">
                NxVoy isn’t just another booking platform. It’s a travel
                assistant with an AI brain! I don’t just find flights or
                recommend places. I think, plan, and remember everything so you
                don’t have to.
              </p>
              <p className="text-sm md:text-base leading-relaxed mt-4 text-[#0e0b2b]">
                People call me an AI, but I’m actually your virtual companion.
                You will feel this only when you get a chance to travel with me.
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <img
                src="https://storage.googleapis.com/nxvoytrips-img/HyperPages/FeatureBox1.png"
                alt="Smart Coordination"
                className="w-full h-auto"
              />
              <img
                src="https://storage.googleapis.com/nxvoytrips-img/HyperPages/FeatureBox2.png"
                alt="Perfect for Families & Groups"
                className="w-full h-auto"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <img
              src="https://storage.googleapis.com/nxvoytrips-img/HyperPages/FeatureBox3.png"
              alt="All Features Accessible"
              className="w-full h-auto"
            />
            <img
              src="https://storage.googleapis.com/nxvoytrips-img/HyperPages/FeatureBox4.png"
              alt="Extraordinary Itineraries"
              className="w-full h-auto"
            />
          </div>
        </div>

        <div className="max-w-6xl mx-auto px-4 py-0 text-center">
          <h2 className="text-2xl md:text-4xl font-bold text-[#0E0B2B] mb-2">
            Curious about how I do it?
          </h2>
          <p className="text-sm md:text-base text-gray-500 mb-12">
            Here’s my step-by-step process:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left">
            {HowShasaSteps.map((step) => (
              <div
                key={step.id}
                className="flex items-center gap-4 mb-12 md:mb-0"
              >
                {step.imagePosition === "left" && (
                  <div className="border border-gray-300 rounded-sm">
                    <img
                      src={step.image}
                      alt={`Step ${step.id}`}
                      className="w-[160px] h-[160px] object-contain"
                    />
                  </div>
                )}
                <div>
                  <h3 className="text-[#1E1E76] font-bold text-3xl mb-2">
                    {step.id}
                  </h3>
                  <p className="font-bold text-[#0E0B2B] mb-1">{step.title}</p>
                  <p className="text-xs text-[#0E0B2B] leading-relaxed">
                    {step.description}
                  </p>
                </div>
                {step.imagePosition === "right" && (
                  <div className="border border-gray-300 rounded-sm">
                    <img
                      src={step.image}
                      alt={`Step ${step.id}`}
                      className="w-[160px] h-[160px] object-contain"
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="flex flex-col items-center justify-center text-center px-4 py-0">
          <h2 className="text-2xl md:text-4xl font-bold mb-6 text-[#0E0B2B]">
            I’m Built for People Who Love to Explore!
          </h2>
          <p className="max-w-3xl text-sm md:text-base text-[#0E0B2B] mb-4 leading-relaxed">
            Whether you’re a solo traveller, a family adventurer, or a group of
            friends planning a reunion, I make sure you get the most out of your
            journey. You dream it, and I’ll make it happen - with no regrets.
          </p>
          <p className="max-w-3xl text-sm md:text-base text-[#0E0B2B] mb-8 leading-relaxed">
            So, where do you wish to go next? You pack your bags, and I’ll
            handle the rest. Let’s make travel effortless, together!
          </p>
          <button
            className="bg-[#1E1E76] hover:bg-[#35358c] text-white text-sm px-6 py-2 rounded-md transition"
            onClick={() => router.push("/chat")}
          >
            Plan Your Next Voyage
          </button>
        </div>
      </section>
      <Footer />
    </div>
  );
};

export default AboutUs;
