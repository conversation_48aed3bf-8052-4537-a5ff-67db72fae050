import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import TripCard from "./tripCard";
import { useState } from "react";

const Trips = () => {
  const [activeTrip, setActiveTrip] = useState("trip-001");

  function handleViewTrip(tripId: string): void {
    throw alert("Function not implemented.");
  }

  const trips = [
    {
      id: "trip-001",
      status: "confirmed" as const,
      type: "flight" as const,
      title: "LONDON TO CHENNAI",
      date: "2025-05-25",
      duration: "5h 58 min",
      imageUrl: "https://example.com/flight-image.jpg",
    },
    {
      id: "trip-002",
      status: undefined, // Changed from empty string to undefined since status is optional
      type: "flight" as const, // Added missing type
      title: "ibis Styles Hotel",
      date: "2025-05-25",
      duration: "5h 58 min",
      imageUrl: "https://example.com/flight-image.jpg",
    },
  ];

  return (
    <>
      <div>
        <Tabs defaultValue="Upcoming-trips" className="">
          <TabsList className="rounded-full h-10 p-0 mb-3 bg-neutral">
            <TabsTrigger
              value="Upcoming-trips"
              className="rounded-full font-semibold h-10 px-6 data-[state=active]:bg-brand data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-brand-grey transition-all duration-200"
            >
              Upcoming trips
            </TabsTrigger>
            <TabsTrigger
              value="Past-trips"
              className="rounded-full font-semibold h-10 px-6 data-[state=active]:bg-brand data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-brand-grey transition-all duration-200"
            >
              Past trips
            </TabsTrigger>
          </TabsList>

          {/* Upcoming Trips */}
          <TabsContent value="Upcoming-trips">
            {trips.map((trip) => (
              <TripCard
                key={trip.id}
                id={trip.id}
                status={trip.status}
                type={trip.type}
                title={trip.title}
                date={trip.date}
                duration={trip.duration}
                isActive={true} // This will show brand background
                onViewTrip={handleViewTrip}
              />
            ))}
          </TabsContent>

          {/* Past Trips */}
          <TabsContent value="Past-trips">
            Change your password here.
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default Trips;
