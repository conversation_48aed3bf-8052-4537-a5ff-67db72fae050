import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import TripCard from "./tripCard";
import FlightInformation from "./flightInformation";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";

const Trips = () => {
  const [activeTrip, setActiveTrip] = useState("trip-001");
  const [selectedTripId, setSelectedTripId] = useState<string | null>(null);
  const [showFlightInfo, setShowFlightInfo] = useState(false);

  function handleViewTrip(tripId: string): void {
    setSelectedTripId(tripId);
    setShowFlightInfo(true);
  }

  function handleBackToTrips(): void {
    setShowFlightInfo(false);
    setSelectedTripId(null);
  }

  const trips = [
    {
      id: "trip-001",
      status: "confirmed" as const,
      type: "flight" as const,
      title: "LONDON TO CHENNAI",
      date: "2025-05-25",
      duration: "5h 58 min",
      imageUrl: "/assets/paris.jpg",
    },
    {
      id: "trip-002",
      status: undefined, // Changed from empty string to undefined since status is optional
      type: "flight" as const, // Added missing type
      title: "ibis Styles Hotel",
      date: "2025-05-25",
      duration: "5h 58 min",
      imageUrl: "/assets/paris.jpg",
    },
  ];

  // Get flight data for the selected trip
  const getFlightDataForTrip = (tripId: string) => {
    const trip = trips.find((t) => t.id === tripId);
    if (!trip) return {};

    // Map trip data to FlightInformation props
    return {
      tripTitle: trip.title,
      tripDate: trip.date,
      bookingReference: `Booking Reference: ${tripId.toUpperCase()}`,
      bookingStatus: trip.status || "confirmed",
      segments: [
        {
          id: "1",
          departure_time: "21:45",
          arrival_time: "07:55*",
          departure_airport: "Heathrow Airport",
          arrival_airport: "Chennai Airport",
          departure_city: "London",
          arrival_city: "Chennai",
          departure_code: "LHR",
          arrival_code: "MAA",
          airline: "Gulf Air Company",
          flight_number: "GF 65",
          aircraft: "Airbus A321neo - Jet",
          duration: trip.duration || "5h 58 min",
          departure_date: trip.date,
          arrival_date: trip.date,
        },
      ],
      passengers: [
        {
          id: "1",
          name: "John Doe",
          type: "adult" as const,
          seat: "12A",
        },
      ],
      contactSupport: {
        chat: true,
        email: true,
      },
      showDownloadTicket: true,
      showFlightChanges: true,
      showCancelFlight: true,
    };
  };

  // If showing flight info, render FlightInformation component
  if (showFlightInfo && selectedTripId) {
    return (
      <div className="relative">
        {/* Back Button */}
        <div className="mb-4">
          <Button
            onClick={handleBackToTrips}
            variant="ghost"
            className="flex items-center text-indigo-600 hover:text-indigo-800 transition-colors p-0"
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            Back to Trips
          </Button>
        </div>

        {/* Flight Information Component */}
        <FlightInformation {...getFlightDataForTrip(selectedTripId)} />
      </div>
    );
  }

  // Default trips list view
  return (
    <>
      <div>
        <Tabs defaultValue="Upcoming-trips" className="">
          <TabsList className="rounded-full h-10 p-0 mb-3 bg-neutral">
            <TabsTrigger
              value="Upcoming-trips"
              className="rounded-full font-semibold h-10 px-6 data-[state=active]:bg-brand data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-brand-grey transition-all duration-200"
            >
              Upcoming trips
            </TabsTrigger>
            <TabsTrigger
              value="Past-trips"
              className="rounded-full font-semibold h-10 px-6 data-[state=active]:bg-brand data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-brand-grey transition-all duration-200"
            >
              Past trips
            </TabsTrigger>
          </TabsList>

          {/* Upcoming Trips */}
          <TabsContent value="Upcoming-trips">
            {trips.map((trip) => (
              <TripCard
                key={trip.id}
                id={trip.id}
                status={trip.status}
                type={trip.type}
                title={trip.title}
                date={trip.date}
                duration={trip.duration}
                isActive={activeTrip === trip.id}
                onViewTrip={handleViewTrip}
              />
            ))}
          </TabsContent>

          {/* Past Trips */}
          <TabsContent value="Past-trips">
            <div className="text-center py-8 text-gray-500">
              No past trips found.
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default Trips;
